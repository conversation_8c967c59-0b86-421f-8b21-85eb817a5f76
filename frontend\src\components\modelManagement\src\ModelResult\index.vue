<template>
  <div class="model-result-page">
    <!-- 自定义窗口标题栏 -->
    <div class="custom-titlebar" @dblclick="handleDoubleClick">
      <div class="title-left">
        <h3>{{ modelDisplayName }}</h3>
      </div>
      <div class="title-right">
        <div class="control-container" :style="{ backgroundColor: epThemeColor }">
          <div class="window-controls">
            <div class="control-btn" @click="minimizeWindow">
              <img :src="minimizeIcon" alt="minimize" class="control-icon" />
            </div>
            <div class="control-btn" @click="maximizeWindow">
              <img :src="maximizeIcon" alt="maximize" class="control-icon" />
            </div>
            <div class="control-btn close-btn" @click="closeWindow">
              <img :src="closeIcon" alt="close" class="control-icon" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域容器 - 添加内边距 -->
    <div class="content-container">
      <!-- 构建进度条 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-content">
          <img :src="loadingIcon" alt="loading" class="loading-icon" />
          <h2 class="loading-title">
            {{
              buildStatus === "processing"
                ? "模型构建中"
                : buildStatus === "completed"
                  ? "加载模型结果"
                  : "准备中"
            }}
          </h2>
          <div class="progress-wrapper">
            <span class="progress-percentage">{{ progress.toFixed(0) }}%</span>
            <el-progress :percentage="progress" :show-text="false" :status="buildStatus === 'error'
                ? 'exception'
                : buildStatus === 'completed'
                  ? 'success'
                  : undefined
              " :indeterminate="false" :duration="5" class="progress-bar" />
          </div>
          <p class="build-status">
            {{ buildStatusMessage || "正在执行复杂计算..." }}
          </p>
        </div>
      </div>

      <div v-else-if="modelResult" class="model-result-layout">
        <div class="model-static-info">
          <ModelIntroduction :model-type="modelType" :model-display-name="modelDisplayName"
            :model-result="modelResult" />
        </div>

        <!-- 目标变量切换按钮 -->
        <div class="model-result-actions">
          <div v-if="targetVariables.length > 1" class="target-variables-tabs">
            <button
              v-for="targetVar in targetVariables"
              :key="targetVar"
              type="button"
              :class="['target-tab-button', { 'is-active': activeTargetVariable === targetVar }]"
              @click="activeTargetVariable = targetVar"
            >
              {{ targetVar }}
            </button>
          </div>
          <el-button type="primary" class="edit-model-btn" @click="handleEditModel">
            编辑模型
          </el-button>
        </div>

        <div class="results-scroll-container">
          <div class="result-card" v-for="(item, index) in entryList" :key="item.key">
            <div class="result-card-header" :class="{ 'is-clickable': item.key === 'save' }"
              @click="item.key === 'save' ? toggleSection('save') : null">
              <div class="header-left">
                <modelResultIcon class="header-icon" />
                <div class="header-text">
                  <h4 class="header-title">{{ item.title }}</h4>
                  <p class="header-desc">{{ item.desc }}</p>
                </div>
              </div>
              <el-button v-if="item.key !== 'save'" class="expand-btn"
                :class="{ 'is-active': visibleSections[item.key] }" @click="toggleSection(item.key)">
                {{ visibleSections[item.key] ? "收起" : "展开" }}
                <el-icon class="el-icon--right" :class="{ 'is-active': visibleSections[item.key] }">
                  <ArrowDown />
                </el-icon>
              </el-button>
            </div>
            <el-collapse-transition>
              <div v-show="visibleSections[item.key]">
                <div class="result-card-body">
                  <component :is="getComponent(item.key)" :model-result="filteredModelResult" :dataset-config="datasetConfig"
                    :task-uid="uid" />
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </div>
      <div v-else class="empty-container">
        <el-empty class="model-empty" description="未找到模型结果数据" />
      </div>
    </div>
    <!-- Edit Model Dialog -->
    <ModelDialog
      :model-value="dialogStore.dialogs[editModelDialogKey]"
      :model-type="modelType ?? ''"
      :dialog-type="derivedModelCategory === 'linear' ? 'linear' : 'ml'"
      :initial-model-config="modelConfigForEdit"
      :edit-mode="true"
      @confirm="handleModelDialogConfirm"
      @close="handleModelDialogClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, reactive, watch } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown } from "@element-plus/icons-vue";
import {
  ModelInfoCard,
  MetricsTable,
  ModelCharts,
  PredictionTable,
  DataSplitTable,
  OptimizationResultTable,
  ModelIntroduction,
  ModelPrediction,
  WhiteBoxFeatureAnalysis,
  BlackBoxFeatureAnalysis,
  ModelDialog
} from "@/components/modelManagement";

import { 
  downloadModelFiles,
  getModelParams,
  editLinearModel,
  editTreeModel,
  editMLModel 
} from "@/api/models/model";

import { exportModelFile } from "@/utils/exportUtils";
// 导入窗口控制图标
import minimizeIcon from "@/assets/svg/minimize_btn.svg?url";
import maximizeIcon from "@/assets/svg/maximize_btn.svg?url";
import closeIcon from "@/assets/svg/close_btn.svg?url";
import loadingIcon from "@/assets/svg/loading.svg?url";
import modelResultIcon from "@/assets/svg/modelResult.svg?component";
import { useEpThemeStoreHook } from "@/store/modules/epTheme";
import { ElCollapseTransition } from "element-plus";
import { useDialogStore } from "@/store/modules/dialog";
import type { ModelConfig } from "@/types/models";
import { getModelInfo } from "@/api/models/model"

// 获取Element Plus主题色
const epThemeStore = useEpThemeStoreHook();
const epThemeColor = computed(() => {
  return epThemeStore.getEpThemeColor;
});

// 窗口控制函数
const minimizeWindow = () => {
  // 使用特殊的IPC事件来控制当前窗口
  window.ipcRenderer.send("minimize-current-window");
};

const maximizeWindow = () => {
  // 使用特殊的IPC事件来控制当前窗口
  window.ipcRenderer.send("maximize-current-window");
};

const closeWindow = () => {
  // 使用特殊的IPC事件来控制当前窗口
  window.ipcRenderer.send("close-current-window");
};

const getModelTypeLabel = (type: string | null) => {
  if (!type) return "模型结果";
  const typeMap = {
    LinearRegression: "多元线性回归",
    Ridge: "岭回归",
    Lasso: "Lasso回归",
    ElasticNet: "弹性网络回归",
    DecisionTreeRegressor: "决策树回归",
    RandomForestRegressor: "随机森林回归",
    XGBoost: "XGBoost回归",
    GradientBoostingRegressor: "梯度提升回归",
    SVR: "支持向量机回归",
    MLPRegressor: "人工神经网络回归",
    local: "本地模型"
  };
  return typeMap[type as keyof typeof typeMap] || type;
};

// 处理双击事件
const handleDoubleClick = (e: MouseEvent) => {
  // 检查是否点击在按钮或其他交互元素上
  const target = e.target as HTMLElement;

  // 如果点击的是窗口控制按钮，不触发最大化
  if (
    target.classList.contains('control-btn') ||
    target.closest('.control-btn')
  ) {
    return;
  }

  // 触发当前窗口的最大化/还原
  window.ipcRenderer.send("maximize-current-window");
};

const baseEntryList = [
  { key: "metrics", title: "评价指标", desc: "查看模型评价指标" },
  { key: "charts", title: "预测结果图表", desc: "查看预测结果的可视化" },
  { key: "predictions", title: "预测数据", desc: "查看预测数据明细" },
  { key: "splits", title: "数据集划分", desc: "查看训练/测试/交叉验证集" },
  { key: "predict", title: "模型预测", desc: "使用新数据进行预测" },
  { key: "save", title: "模型保存", desc: "下载已训练模型文件" },
];

const visibleSections = reactive({
  introduction: true,
  metrics: true,
  charts: true,
  predictions: false,
  splits: false,
  optimization: false,
  whiteBoxFeatureAnalysis: false,
  blackBoxFeatureAnalysis: false,
  predict: false
});

const derivedModelCategory = computed<"linear" | "tree" | "ml">(() => {
  if (!modelType.value) {
    return "linear";
  }
  const linearModels = ["LinearRegression", "Ridge", "Lasso", "ElasticNet"];
  if (linearModels.includes(modelType.value)) {
    return "linear";
  }
  const treeModels = [
    "DecisionTreeRegressor",
    "RandomForestRegressor",
    "XGBoost",
    "GradientBoostingRegressor"
  ];
  if (treeModels.includes(modelType.value)) {
    return "tree";
  }
  return "ml";
});

const entryList = computed(() => {
  const list = [...baseEntryList];
  const category = derivedModelCategory.value;
  let insertIndex = 2; // Start inserting after "评价指标" and "预测结果图表"

  // Show white-box analysis for tree models if data is available
  if (category === "linear" || category === "tree") {
    list.splice(insertIndex, 0, {
      key: "whiteBoxFeatureAnalysis",
      title: "白盒特征分析",
      desc: "查看模型特征权重图及模型结构"
    });
    insertIndex++;
  }

  // Show black-box analysis for all non-linear models (tree and others)
  list.splice(insertIndex, 0, {
    key: "blackBoxFeatureAnalysis",
    title: "黑盒特征分析",
    desc: "查看SHAP和PFI分析图"
  });
  insertIndex++;

      if (
      modelResult.value?.optimizedResult ||
      modelResult.value?.optimizedParams
    ) {
      // 检查是否有有效的优化数据（不是所有目标变量都为null）
      const hasValidOptimizationData = () => {
        if (modelResult.value?.optimizedResult) {
          const hasValidResult = Object.values(modelResult.value.optimizedResult).some(value => value !== null);
          if (hasValidResult) return true;
        }
        if (modelResult.value?.optimizedParams) {
          const hasValidParams = Object.values(modelResult.value.optimizedParams).some(value => value !== null);
          if (hasValidParams) return true;
        }
        return false;
      };

    if (hasValidOptimizationData()) {
      list.push({
        key: "optimization",
        title: "超参数优化",
        desc: "查看超参数寻优过程"
      });
    }
  }
  return list;
});


type ViewKey =
  | "entry"
  | "metrics"
  | "charts"
  | "predictions"
  | "splits"
  | "optimization"
  | "predict"
  | "whiteBoxFeatureAnalysis"
  | "blackBoxFeatureAnalysis";

async function handleSaveModel() {
  try {
    await ElMessageBox.confirm("确认下载该模型文件？", "模型保存", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    });
    if (!uid.value) {
      ElMessage.error("无法获取模型任务ID，无法下载。");
      return;
    }

    const response = await downloadModelFiles([uid.value]);

    if (response instanceof Blob) {
      // 使用 exportUtils 导出模型文件，传入单个UID
      await exportModelFile(response, `${uid.value}`, [uid.value]);
      ElMessage.success("模型已开始下载");
    } else {
      ElMessage.error("下载失败：服务器未返回有效文件。");
      console.error("Download failed:", response);
    }
  } catch (error) {
    // 用户取消或下载失败
    if (error === "cancel") {
      ElMessage.info("下载已取消。");
    } else if (error) {
      ElMessage.error(`下载模型失败: ${(error as Error).message}`);
      console.error("Error saving model:", error);
    }
  }
}

const modelResult = ref<any>(null);
const route = useRoute();

// 模型构建状态和进度
const isLoading = ref(true);
const progress = ref(0);
const buildStatus = ref<"waiting" | "processing" | "completed" | "error">(
  "waiting",
);
const buildStatusMessage = ref("");
const uid = ref<string | null>(null);
const modelType = ref<string | null>(null);
const modelDisplayName = computed(() => getModelTypeLabel(modelType.value));

const dialogStore = useDialogStore();
const showEditModelDialog = ref(false);
const modelConfigForEdit = ref<ModelConfig | undefined>(undefined);

const isRetrainFromEdit = ref(false);

const editModelDialogKey = computed(() => {
  if (modelType.value) {
    const linearModels = ["LinearRegression", "Ridge", "Lasso", "ElasticNet"];
    return linearModels.includes(modelType.value) ? "linearModel" : "mlModel";
  }
  return "mlModel"; // Default to mlModel if type is unknown
});

// 目标变量
const targetVariables = computed(() => {
  if (!modelResult.value) return [];
  
  // 从接口返回的数据中提取目标变量
  // 根据数据结构，目标变量可能在eval、featureAnalysis等字段中
  const possibleTargetFields = ['eval', 'featureAnalysis', 'optimizedParams', 'optimizedResult'];
  const targetVars = new Set<string>();
  
  possibleTargetFields.forEach(field => {
    if (modelResult.value[field] && typeof modelResult.value[field] === 'object') {
      Object.keys(modelResult.value[field]).forEach(key => {
        // 过滤掉一些特殊字段
        if (key !== 'uid' && key !== 'xTrain' && key !== 'yTrain' && key !== 'xTest' && key !== 'yTest') {
          targetVars.add(key);
        }
      });
    }
  });
  
  const result = Array.from(targetVars);
  console.log('Identified target variables:', result, 'from model result:', modelResult.value);
  return result;
});

const activeTargetVariable = ref('');

// 当目标变量列表更新时，设置默认选中的目标变量
watch(targetVariables, (newTargetVars) => {
  if (newTargetVars.length > 0 && !newTargetVars.includes(activeTargetVariable.value)) {
    activeTargetVariable.value = newTargetVars[0];
  }
}, { immediate: true });

// 使用computed属性来缓存过滤后的模型结果，避免重复计算
const filteredModelResult = computed(() => {
  if (!modelResult.value) return modelResult.value;
  
  // 如果没有目标变量，直接返回原始数据
  if (targetVariables.value.length === 0) {
    return modelResult.value;
  }
  
  // 根据选中的目标变量，提取对应的数据
  const filteredResult = { ...modelResult.value };
  
  // 过滤eval数据 - 直接提取目标变量的数据
  if (filteredResult.eval) {
    const targetEval = filteredResult.eval[activeTargetVariable.value];
    filteredResult.eval = targetEval; // 直接赋值，不包装
  }
  
  // 过滤featureAnalysis数据 - 直接提取目标变量的数据
  if (filteredResult.featureAnalysis) {
    const targetFeatureAnalysis = filteredResult.featureAnalysis[activeTargetVariable.value];
    filteredResult.featureAnalysis = targetFeatureAnalysis; // 直接赋值，不包装
  }
  
  // 过滤optimizedParams数据 - 直接提取目标变量的数据
  if (filteredResult.optimizedParams) {
    const targetOptimizedParams = filteredResult.optimizedParams[activeTargetVariable.value];
    filteredResult.optimizedParams = targetOptimizedParams; // 直接赋值，不包装
  }
  
  // 过滤optimizedResult数据 - 直接提取目标变量的数据
  if (filteredResult.optimizedResult) {
    const targetOptimizedResult = filteredResult.optimizedResult[activeTargetVariable.value];
    filteredResult.optimizedResult = targetOptimizedResult; // 直接赋值，不包装
  }
  
  // 处理modelParams字段 - 数据集划分组件依赖此字段
  if (filteredResult.modelParams) {
    const targetModelParamsResult = filteredResult.modelParams[activeTargetVariable.value];
    filteredResult.modelParams = targetModelParamsResult; // 直接赋值，不包装
  }
  
  // 添加调试信息
  console.log('Filtered model result for target variable:', activeTargetVariable.value, filteredResult);
  
  return filteredResult;
});




// 处理来自Electron IPC的模型进度更新
const handleModelProgressUpdate = (_event: Event, data: any) => {
  console.log("IPC model progress update received:", data);

  // 统一使用 uid
  const event_uid = data.uid;

  if (event_uid == uid.value) {
    buildStatus.value = data.status;
    buildStatusMessage.value = data.message || "正在构建模型...";

    if (data.progress !== undefined) {
      progress.value = Math.min(99, data.progress); // 保留最后1%给加载结果
    }

    // 注意：不再在这里处理completed状态
    // completed状态现在由model_complete事件处理
    if (data.status === "error") {
      ElMessage.error(`模型构建失败: ${data.message || "发生未知错误"}`);
    }
  }
};

// 处理来自Electron IPC的模型完成事件
const handleModelComplete = async (_event: Event, data: any) => {
  console.log("IPC model complete event received:", data);

  if (data.uid !== uid.value) return;

  try {
    if (isRetrainFromEdit.value) {
      // 编辑模型：调一次 API 确认最终结果
      console.log("模型编辑完成后，重新从 API 获取最新模型结果...");
      const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
      await sleep(5000);
      const res = await getModelInfo(uid.value!);
      if (res.code === 200 && res.data) {
        await window.ipcRenderer.send("cache_model_result", {
          uid: uid.value,
          result: res.data
        });
        modelResult.value = res.data;
        isLoading.value = false;
        ElMessage.success("模型训练完成并已更新最新结果");
      } else {
        ElMessage.error(res.msg || "获取最新模型结果失败");
        isLoading.value = false;
      }
    } else {
      // 普通建模：直接使用 socket 带回来的结果
      if (data.result) {
        modelResult.value = data.result;
        isLoading.value = false;
        console.log("从 model_complete 事件直接获取到模型结果:", modelResult.value);

        ElMessage.success({
          message: "模型构建完成",
          duration: 2000,
        });
      } else {
        console.warn("model_complete 事件没有包含结果数据");
        ElMessage.error("未能获取模型结果，请尝试刷新页面");
        isLoading.value = false;
      }
    }
  } catch (err) {
    console.error("获取模型详情失败:", err);
    ElMessage.error("获取模型详情失败");
    isLoading.value = false;
  } finally {
    isRetrainFromEdit.value = false;
  }

  // 更新状态为完成
  progress.value = 100;
  buildStatus.value = "completed";
  buildStatusMessage.value = "模型构建完成";
};

// 注册事件监听器
const setupEventListeners = () => {
  // 监听Electron IPC事件
  if (window.ipcRenderer) {
    window.ipcRenderer.on("model_progress_update", handleModelProgressUpdate);
    window.ipcRenderer.on("model_complete", handleModelComplete);
  }
};

// 移除事件监听器
const removeEventListeners = () => {
  if (window.ipcRenderer) {
    window.ipcRenderer.off("model_progress_update", handleModelProgressUpdate);
    window.ipcRenderer.off("model_complete", handleModelComplete);
  }
};

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  removeEventListeners();
});

const datasetConfig = computed(() => {
  return {
    order: ["train", "test", "cv", "loocv"] as const,
    titles: {
      train: "训练集",
      test: "测试集",
      cv: "交叉验证",
      loocv: "留一法验证",
    },
  };
});

const getComponent = (key: string) => {
  switch (key) {
    case "metrics":
      return MetricsTable;
    case "charts":
      return ModelCharts;
    case "predictions":
      return PredictionTable;
    case "splits":
      return DataSplitTable;
    case "optimization":
      return OptimizationResultTable;
    case "predict":
      return ModelPrediction;
    case "whiteBoxFeatureAnalysis":
      return WhiteBoxFeatureAnalysis;
    case "blackBoxFeatureAnalysis":
      return BlackBoxFeatureAnalysis;
    default:
      return null;
  }
};

const toggleSection = (key: string) => {
  if (key === "save") {
    handleSaveModel();
    return;
  }
  if (visibleSections.hasOwnProperty(key)) {
    visibleSections[key] = !visibleSections[key];
  }
};

const handleEditModel = async () => {
  if (!uid.value) {
    ElMessage.error("无法获取模型任务ID，无法编辑。");
    return;
  }

  try {
    const response = await getModelParams(uid.value);
    if (response.code === 200 && response.data) {
      modelConfigForEdit.value = response.data as ModelConfig;
      dialogStore.showDialog(editModelDialogKey.value);
    } else {
      ElMessage.error(response.msg || "获取模型参数失败");
    }
  } catch (error) {
    ElMessage.error(`获取模型参数失败: ${(error as Error).message}`);
    console.error("Error fetching model parameters:", error);
  }
};

const handleModelDialogConfirm = async (config: ModelConfig) => {
  console.log("ModelDialog confirmed with config:", config);
  
  if (!uid.value) {
    ElMessage.error("无法获取模型任务ID，无法编辑。");
    return;
  }

  try {
    // 将uid添加到config中
    const configWithUid = {
      ...config,
      uid: uid.value
    };

    // 根据模型类别选择对应的编辑函数
    let editFunction;
    const category = derivedModelCategory.value;
    
    switch (category) {
      case "linear":
        editFunction = editLinearModel;
        break;
      case "tree":
        editFunction = editTreeModel;
        break;
      case "ml":
        editFunction = editMLModel;
        break;
      default:
        ElMessage.error("未知的模型类别，无法编辑。");
        return;
    }

    isRetrainFromEdit.value = true;

    // 显示进度条
    isLoading.value = true;
    progress.value = 0;
    buildStatus.value = "processing";
    buildStatusMessage.value = "正在编辑模型配置...";

    // 隐藏对话框
    dialogStore.hideDialog(editModelDialogKey.value);

    window.ipcRenderer.send("request_model_info", { uid: uid.value });

    // 调用编辑API
    const response = await editFunction(configWithUid);
    
    if (response.code === 200) {
      ElMessage.success("模型配置已更新，开始重新训练模型...");
      
      // 更新进度状态
      progress.value = 30;
      buildStatusMessage.value = "模型配置更新完成，等待重新训练...";
      
      // 确保事件监听器已设置（通常在onMounted中已设置，但这里确保）
      if (window.ipcRenderer && !window.ipcRenderer.eventNames().includes("model_progress_update")) {
        setupEventListeners();
      }
      
      // 通知主进程开始监听重新训练进度
      if (window.ipcRenderer) {
        window.ipcRenderer.send("start_model_retraining", {
          uid: uid.value,
          modelType: modelType.value,
        });
      }
      
      // 监听重新训练完成
      const handleRetrainingComplete = (event: Event, data: any) => {
        if (data.uid === uid.value) {
          if (window.ipcRenderer) {
            window.ipcRenderer.off("model_complete", handleRetrainingComplete);
          }
        }
      };
      
      // 临时添加完成事件监听器（如果还没有的话）
      if (window.ipcRenderer) {
        window.ipcRenderer.on("model_complete", handleRetrainingComplete);
      }
      
    } else {
      ElMessage.error(response.msg || "编辑模型失败");
      // 重置加载状态
      isLoading.value = false;
      buildStatus.value = "error";
      buildStatusMessage.value = "编辑失败";
    }
    
  } catch (error) {
    ElMessage.error(`编辑模型失败: ${(error as Error).message}`);
    console.error("Error editing model:", error);
    
    // 重置加载状态
    isLoading.value = false;
    buildStatus.value = "error";
    buildStatusMessage.value = "编辑失败";
    isRetrainFromEdit.value = false;
  }
};

const handleModelDialogClose = () => {
  console.log("ModelDialog closed.");
  modelConfigForEdit.value = undefined; // Reset the config when dialog closes
  dialogStore.hideDialog(editModelDialogKey.value);
};

onMounted(() => {
  try {
    // Manually set theme color for the new window
    const storedThemeColor = epThemeStore.getEpThemeColor;
    if (storedThemeColor) {
      document.documentElement.style.setProperty(
        "--el-color-primary",
        storedThemeColor,
      );
      epThemeStore.setEpThemeColor(storedThemeColor);
    }
    // 设置事件监听
    setupEventListeners();

    // 获取查询参数
    const uid_param = route.query.uid as string | undefined;
    const modelTypeParam = route.query.modelType as string | undefined;

    console.log("Route query params:", {
      uid: uid_param,
      modelType: modelTypeParam,
    });

    // 先检查路由query参数中的uid
    if (uid_param) {
      uid.value = uid_param;
    }

    // 检查路由参数中的modelType
    if (modelTypeParam) {
      modelType.value = modelTypeParam;
    }

    // 如果URL参数不可用，尝试从URL搜索参数获取
    if (!uid.value) {
      const url = new URL(window.location.href);

      // 尝试获取uid，兼容各种参数名
      const urlUid = url.searchParams.get("uid");
      if (urlUid) {
        uid.value = urlUid;
      }

      if (!modelType.value) {
        modelType.value = url.searchParams.get("modelType") || null;
      }
    }

    console.log("处理后的参数:", {
      uid: uid.value,
      modelType: modelType.value,
    });

    // 注册窗口加载事件到Electron
    if (window.ipcRenderer && uid.value) {
      window.ipcRenderer.send("model_result_window_ready", {
        uid: uid.value,
        modelType: modelType.value,
      });
    }

    if (uid.value) {
      // 如果只有uid，则显示进度条并等待构建完成
      console.log("Starting progress tracking for uid:", uid.value);

      buildStatus.value = "waiting";
      buildStatusMessage.value = "请稍后...";

      // 保持isLoading为true，显示进度条
      isLoading.value = true;
    } else {
      ElMessage.warning("未找到模型结果数据或任务ID");
      console.error("未找到有效的uid或结果数据", {
        uid: uid.value,
      });
      isLoading.value = false;
    }
  } catch (err) {
    ElMessage.error("数据加载失败: " + (err as Error).message);
    console.error("Error loading model result:", err);
    isLoading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.model-result-page {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  -webkit-app-region: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

.model-result-actions {
  display: flex;
  align-items: center; /* Vertically aligns items in the middle */
  margin: 10px 20px 20px 20px;
}

.edit-model-btn {
  margin-left: auto; /* This pushes the button to the far right */
}

.custom-titlebar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  padding: 0;
  background-color: #fff;
  -webkit-app-region: drag;
  border-bottom: 1px solid #e4e7ed;

  .title-left {
    display: flex;
    align-items: center;
    padding-left: 20px;
    height: 100%;

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #303133;
    }
  }

  .title-right {
    display: flex;
    align-items: center;
    height: 100%;
    -webkit-app-region: no-drag;
  }

  .control-container {
    height: 100%;
    width: 124px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 24.5px 0 0 24.5px;
    background-color: v-bind(epThemeColor);
    -webkit-app-region: no-drag;
  }

  .window-controls {
    display: flex;
    color: white;
    -webkit-app-region: no-drag;
    height: 100%;
    align-items: center;
    justify-content: center;
  }

  .control-btn {
    padding: 0 10px;
    cursor: pointer;
    line-height: 30px;
    height: 30px;
    -webkit-app-region: no-drag;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .control-icon {
      width: 10px;
      height: 10px;
      display: inline-block;
      filter: brightness(0) invert(1);
      /* 将图标转为白色 */
    }
  }

  .close-btn:hover {
    background-color: #e81123;
  }
}

/* 深色模式适配 */
html.dark .custom-titlebar {
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);

  h3 {
    color: var(--el-text-color-primary);
  }
}

/* 添加内容容器，提供统一的内边距 */
.content-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 修复flexbox溢出问题 */
}

.model-result-layout {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .el-button {
    font-size: 16px;
  }

  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-left: 8px;
  }
}

.info-alert {
  margin-top: 20px;
}

.model-static-info {
  margin: 0;
}

.result-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 0px 20px 0px rgba(61, 61, 61, 0.08);
  margin: 20px 15px;
  overflow: hidden;

  .result-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    transition: background-color 0.3s;

    &:hover {
      background-color: #fafcfe;
    }
  }

  .result-card-header.is-clickable {
    cursor: pointer;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon {
    font-size: 24px;
    color: var(--el-color-primary);
  }

  .header-text {
    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .header-desc {
      font-size: 13px;
      color: var(--el-text-color-regular);
      margin-top: 4px;
    }
  }

  .expand-btn {
    font-size: 14px;
    background: #f9fbff;

    .el-icon--right {
      margin-left: 4px;
      transition: transform 0.3s;
    }

    .el-icon--right.is-active {
      transform: rotate(180deg);
    }
  }

  .result-card-body {
    padding: 20px;
    border-top: none;
  }
}

/* 添加详情视图容器样式 */
.detail-view-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 深色模式适配 */
html.dark .detail-view-container {
  background-color: var(--el-bg-color);
}

html.dark .page-header .page-title {
  color: var(--el-text-color-primary);
}

html.dark .result-card {
  background-color: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-light);

  .result-card-header:hover {
    background-color: var(--el-fill-color-darker);
  }

  .result-card-body {
    border-top-color: var(--el-border-color-light);
  }
}


.result-entry-list {
  margin-top: 20px;
}

.entry-row {
  row-gap: 20px;
}

.entry-card {
  cursor: pointer;

  // Most styles are now in .model-card global class
  .entry-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      font-size: 24px;
      color: #409eff;
    }
  }

  .entry-desc {
    color: #909399;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
  }
}

.sub-header {
  display: flex;
  align-items: center;
  background: white;
  padding: 16px 20px;
  border-radius: 12px;
  margin: 0 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;

  .el-button {
    font-weight: 500;
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }

  .sub-title {
    display: flex;
    align-items: center;
    margin-left: 8px;
    font-size: 1.125rem;
    /* 18px */
    line-height: 1.75rem;
    /* 28px */
    font-weight: 500;
    color: #303133;

    .el-icon {
      margin-right: 4px;
      color: #409eff;
    }
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
  max-width: 400px;
  padding: 32px;
  text-align: center;
}

.loading-icon {
  width: 138px;
  height: 138px;
  margin-left: 55px;
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
}

.progress-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-color-primary);
  min-width: 40px;
  text-align: left;
}

.progress-bar {
  flex-grow: 1;
}

.build-status {
  color: #909399;
  font-size: 14px;
}

.build-progress {
  width: 80%;
  max-width: 800px;
  padding: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  text-align: center;

  h2 {
    margin-bottom: 24px;
    font-size: 1.5rem;
    color: #303133;
  }

  .el-progress {
    margin-bottom: 20px;
  }

  .build-status {
    margin-top: 16px;
    color: #606266;
    font-size: 1rem;
  }
}

/* 深色模式适配 */
html.dark .sub-header {
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
}

html.dark .loading-title {
  color: var(--el-text-color-primary);
}

html.dark .build-status {
  color: var(--el-text-color-regular);
}

html.dark .model-result-layout {
  background-color: var(--el-bg-color);
}

html.dark .results-scroll-container {
  background-color: var(--el-bg-color);
}

.results-scroll-container {
  flex: 1;
  overflow-y: auto;

  /* 滚动条整体部分 */
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.1);
    transition: all 0.2s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.target-variables-tabs {
  display: flex;
  gap: 12px;
  padding: 0;
  background-color: transparent;
  overflow-x: auto;
  -webkit-app-region: no-drag;
  align-items: center;

  .target-tab-button {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 100px;
    height: 36px;
    border-radius: 24px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    background: rgba(206, 206, 206, 0.1);
    color: #666;
    white-space: nowrap;

    &:hover {
      background: rgba(206, 206, 206, 0.2);
    }

    &.is-active {
      background: rgba(0, 93, 255, 0.1);
      border: 1px solid #005DFF;
      color: #005DFF;
      font-weight: 500;
    }
  }
  .edit-model-btn {
    margin-left: auto; /* Push the button to the right */
  }
}

/* 深色模式适配 */
html.dark .target-variables-tabs {
  background-color: transparent;
}

html.dark .target-tab-button {
  background: rgba(206, 206, 206, 0.1);
  color: #666;

  &.is-active {
    background: rgba(0, 93, 255, 0.1);
    border: 1px solid #005DFF;
    color: #005DFF;
  }
}

@media (max-width: 768px) {
  .model-result-page {
    padding: 0;
  }

  .content-container {
    padding: 12px;
  }

  .result-entry-list {
    padding: 0;
  }

  .entry-card :deep(.el-card__body) {
    padding: 16px;
  }

  .entry-title {
    font-size: 18px;
  }

  .sub-header {
    padding: 12px 16px;
    margin: 0 0 16px;
  }

  .build-progress {
    width: 95%;
    padding: 20px;

    h2 {
      font-size: 1.25rem;
      margin-bottom: 16px;
    }
  }
}
</style>

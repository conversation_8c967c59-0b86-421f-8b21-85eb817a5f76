<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useNav } from "@/layout/hooks/useNav";
import LayNavMix from "../lay-sidebar/NavMix.vue";
import LaySidebarTopCollapse from "../lay-sidebar/components/SidebarTopCollapse.vue";
import logo from "@/assets/svg/logo.svg?url";
import minimizeIcon from "@/assets/svg/minimize_btn.svg?url";
import maximizeIcon from "@/assets/svg/maximize_btn.svg?url";
import closeIcon from "@/assets/svg/close_btn.svg?url";
import { useEpThemeStoreHook } from "@/store/modules/epTheme";

const {
  layout,
  device,
  logout,
  onPanel,
  pureApp,
  username,
  userAvatar,
  avatarsStyle,
  toggleSideBar,
} = useNav();

// 获取Element Plus主题色
const epThemeStore = useEpThemeStoreHook();
const epThemeColor = computed(() => {
  return epThemeStore.getEpThemeColor;
});

// 检查是否为开发环境
const isDev = computed(() => {
  return process.env.NODE_ENV === "development";
});

// 窗口状态管理
const isMaximized = ref(false);

const menus = ref([
  { name: "文件", event: "show-file-menu" },
  { name: "项目", event: "show-project-menu" },
  { name: "编辑", event: "show-edit-menu" },
  { name: "显示", event: "show-view-menu" },
  // 只在开发环境下显示开发菜单
  ...(isDev.value ? [{ name: "开发", event: "show-dev-menu" }] : []),
  { name: "关于", event: "show-about-menu" },
]);

const showMenu = (event: string, mouseEvent: MouseEvent) => {
  const target = mouseEvent.currentTarget as HTMLElement;
  const rect = target.getBoundingClientRect();
  const position = { x: rect.left, y: rect.bottom };
  window.ipcRenderer.send(event, position);
};

const minimizeWindow = () => {
  window.ipcRenderer.send("minimize-window");
};

const maximizeWindow = () => {
  window.ipcRenderer.send("maximize-window");
};

const closeWindow = () => {
  window.ipcRenderer.send("close-window");
};

// 窗口状态监听
const updateWindowState = async () => {
  if (window.ipcRenderer) {
    try {
      const maximized = await window.ipcRenderer.invoke("is-window-maximized");
      isMaximized.value = maximized;
    } catch (error) {
      console.warn("Failed to get window maximized state:", error);
    }
  }
};

// 拖拽和双击状态管理
let isDragging = false;
let dragStartTime = 0;
let clickCount = 0;
let clickTimer: NodeJS.Timeout | null = null;

// 检查是否为交互元素的辅助函数
const isInteractiveElement = (target: HTMLElement): boolean => {
  return (
    target.classList.contains("menu-item") ||
    target.classList.contains("control-btn") ||
    target.closest(".menu-item") !== null ||
    target.closest(".control-btn") !== null ||
    target.closest(".logo") !== null ||
    target.closest(".window-controls") !== null
  );
};

// 处理鼠标按下事件
const handleMouseDown = (e: MouseEvent) => {
  if (e.button !== 0) return; // 只处理左键

  const target = e.target as HTMLElement;
  if (isInteractiveElement(target)) {
    return; // 交互元素不处理拖拽
  }

  isDragging = false;
  dragStartTime = Date.now();
  clickCount++;

  // 处理双击
  if (clickCount === 1) {
    clickTimer = setTimeout(() => {
      clickCount = 0;
      clickTimer = null;
    }, 300); // 300ms内的第二次点击算作双击
  } else if (clickCount === 2) {
    // 双击事件
    if (clickTimer) {
      clearTimeout(clickTimer);
      clickTimer = null;
    }
    clickCount = 0;
    maximizeWindow();
    return;
  }

  // 添加鼠标移动监听来检测拖拽
  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};

// 处理鼠标移动事件
const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging) {
    const timeDiff = Date.now() - dragStartTime;
    // 如果移动时间超过100ms，认为是拖拽而不是双击
    if (timeDiff > 100) {
      isDragging = true;
      // 清除双击计时器
      if (clickTimer) {
        clearTimeout(clickTimer);
        clickTimer = null;
        clickCount = 0;
      }
      // 开始拖拽
      window.ipcRenderer.send("start-drag");
      // 移除事件监听器，因为拖拽已经开始
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    }
  }
};

// 处理鼠标释放事件
const handleMouseUp = () => {
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);

  // 重置拖拽状态
  setTimeout(() => {
    isDragging = false;
  }, 50);
};

// 监听窗口状态变化
onMounted(() => {
  updateWindowState();

  // 监听窗口状态变化事件
  if (window.ipcRenderer) {
    window.ipcRenderer.on("window-maximized", () => {
      isMaximized.value = true;
    });

    window.ipcRenderer.on("window-unmaximized", () => {
      isMaximized.value = false;
    });

    window.ipcRenderer.on("window-restored", () => {
      isMaximized.value = false;
    });
  }
});

onUnmounted(() => {
  // 清理事件监听器
  if (window.ipcRenderer) {
    window.ipcRenderer.removeAllListeners("window-maximized");
    window.ipcRenderer.removeAllListeners("window-unmaximized");
    window.ipcRenderer.removeAllListeners("window-restored");
  }
});
</script>

<template>
  <div class="navbar" @mousedown="handleMouseDown">
    <LaySidebarTopCollapse
      v-if="device === 'mobile'"
      class="hamburger-container"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    />

    <!-- 菜单按钮 -->
    <div
      v-if="layout !== 'mix' && device !== 'mobile'"
      id="laymenu"
      class="menu-container"
    >
      <div class="title-bar-left">
        <img :src="logo" alt="logo" class="logo" />
        <div class="left-menu">
          <div
            v-for="menu in menus"
            :key="menu.name"
            class="menu-item"
            @click="showMenu(menu.event, $event)"
          >
            {{ menu.name }}
          </div>
        </div>
      </div>
      <div class="title-bar-right">
        <div class="control-container" :style="{ backgroundColor: epThemeColor }">
          <div class="window-controls">
            <div class="control-btn" @click="minimizeWindow">
              <img :src="minimizeIcon" alt="minimize" class="control-icon" />
            </div>
            <div class="control-btn" @click="maximizeWindow">
              <img :src="maximizeIcon" alt="maximize" class="control-icon" />
            </div>
            <div class="control-btn close-btn" @click="closeWindow">
              <img :src="closeIcon" alt="close" class="control-icon" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <LayNavMix v-if="layout === 'mix'" />
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  height: 70px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .hamburger-container {
    float: left;
    height: 100%;
    line-height: 70px;
    cursor: pointer;
  }

  .vertical-header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 280px;
    height: 48px;
    color: #000000d9;

    .el-dropdown-link {
      display: flex;
      align-items: center;
      justify-content: space-around;
      height: 48px;
      padding: 10px;
      color: #000000d9;
      cursor: pointer;

      p {
        font-size: 14px;
      }

      img {
        width: 22px;
        height: 22px;
        border-radius: 50%;
      }
    }
  }

  .breadcrumb-container {
    float: left;
    margin-left: 16px;
  }
}

.logout {
  width: 120px;

  :deep(.el-dropdown-menu__item) {
    display: inline-flex;
    flex-wrap: wrap;
    min-width: 100%;
  }
}

.menu-container {
  padding: 0 0 0 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  background-color: #fff;
  gap: 20px;
}

/* 深色模式适配 */
html.dark .menu-container {
  background-color: var(--el-bg-color);
}

.title-bar-left {
  display: flex;
  align-items: center;
  pointer-events: auto;
  width: -webkit-fill-available;
}

.title-bar-right {
  display: flex;
  align-items: center;
  pointer-events: auto;
}

.left-menu {
  display: flex;
  margin-left: 15px;
  pointer-events: auto;
}

.logo {
  margin-right: 8px;
  pointer-events: auto;
  cursor: pointer;
}

.title {
  font-size: 14px;
  font-weight: bold;
}

.menu-item {
  padding: 0 15px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  height: 30px;
  line-height: 30px;
  pointer-events: auto;

  &:hover {
    border-radius: 8px;
    background-color: #e4e6e7;
  }
}

.control-container {
  position: relative;
  width: 124px;
  height: 49px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 24.5px 0 0 24.5px;
  pointer-events: auto;
}

.admin-text {
  font-size: 13px;
  margin-right: 15px;
}

.control-icon {
  width: 10px;
  height: 10px;
  display: inline-block;
  vertical-align: middle;
}

.window-controls {
  display: flex;
  color: white;
  pointer-events: auto;
}

.control-btn {
  padding: 0 10px;
  cursor: pointer;
  line-height: 30px;
  height: 30px;
  pointer-events: auto;

  &:hover {
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.2);
  }
}

.close-btn:hover {
  background-color: #e81123;
}
</style>

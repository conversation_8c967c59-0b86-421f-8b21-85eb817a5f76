import { ipc<PERSON><PERSON>, BrowserWindow, app } from "electron";
import { tmpdir } from "node:os";
import { join, dirname } from "node:path";
import type { WindowManager } from "./WindowManager";
import type { FileSystemHandler } from "./FileSystemHandler";
import type { DialogHandler } from "./DialogHandler";
import type { MenuManager } from "./MenuManager";

/**
 * IPC 通信处理器 - 负责处理所有 IPC 通信
 */
export class IPCHandler {
  private windowManager: WindowManager;
  private fileSystemHandler: FileSystemHandler;
  private dialogHandler: DialogHandler;
  private menuManager: MenuManager;

  constructor(
    windowManager: WindowManager,
    fileSystemHandler: FileSystemHandler,
    dialogHandler: DialogHandler,
    menuManager: MenuManager,
  ) {
    this.windowManager = windowManager;
    this.fileSystemHandler = fileSystemHandler;
    this.dialogHandler = dialogHandler;
    this.menuManager = menuManager;
    this.setupIpcHandlers();
  }

  /**
   * 设置所有 IPC 处理器
   */
  private setupIpcHandlers() {
    this.setupModelHandlers();
    this.setupWindowHandlers();
    this.setupFileSystemHandlers();
    this.setupDialogHandlers();
    this.setupMenuHandlers();
    this.setupUtilityHandlers();
  }

  /**
   * 设置模型相关的 IPC 处理器
   */
  private setupModelHandlers() {
    // 监听模型进度更新
    ipcMain.on("model_progress_update", (event, data) => {
      const uid = data.uid;
      const targetWindow = this.windowManager.getTaskWindow(uid);

      if (targetWindow && !targetWindow.isDestroyed()) {
        targetWindow.webContents.send("model_progress_update", data);
      }
    });

    // 监听模型完成事件
    ipcMain.on("model_complete", (event, data) => {
      const uid = data.uid;
      const targetWindow = this.windowManager.getTaskWindow(uid);

      this.windowManager.cachePendingModelCompleteEvent(uid, data);

      if (targetWindow && !targetWindow.isDestroyed()) {
        targetWindow.webContents.send("model_complete", data);
      }
    });

    // 监听从渲染器发送的预取结果并暂存
    ipcMain.on("cache_model_result", (event, { uid, result }) => {
      if (uid && result) {
        this.windowManager.cachePreFetchedResult(uid, result);
      }
    });

    // 监听模型结果窗口准备就绪事件
    ipcMain.on("model_result_window_ready", (event, data) => {
      const uid = data.uid;
      if (!uid) return;

      const resultWindow = BrowserWindow.fromWebContents(event.sender);
      if (resultWindow) {
        this.windowManager.registerTaskWindow(uid, resultWindow);

        const preFetchedResult = this.windowManager.getPreFetchedResult(uid);
        if (preFetchedResult) {
          resultWindow.webContents.send("model_complete", {
            uid,
            result: preFetchedResult,
          });
          this.windowManager.deletePreFetchedResult(uid);
          return;
        }

        const pendingData =
          this.windowManager.getPendingModelCompleteEvent(uid);
        if (pendingData) {
          resultWindow.webContents.send("model_complete", pendingData);
          this.windowManager.deletePendingModelCompleteEvent(uid);
        }
      }
    });

    // 监听模型信息请求
    ipcMain.on("request_model_info", async (event, data) => {
      const uid = data.uid;
      if (!uid) {
        event.sender.send("model_info_response", {
          code: 400,
          msg: "无效的任务ID",
        });
        return;
      }

      const resultWindow = BrowserWindow.fromWebContents(event.sender);
      if (resultWindow) {
        this.windowManager.registerTaskWindow(uid, resultWindow);
      }

      const mainWindow = this.windowManager.getMainWindow();
      if (!mainWindow || mainWindow.isDestroyed()) {
        event.sender.send("model_info_response", {
          code: 500,
          msg: "主窗口不可用",
        });
        return;
      }

      try {
        const result = await mainWindow.webContents.executeJavaScript(`
          (function() {
            return new Promise((resolve) => {
              const socket = window.socketInstance;
              if (!socket || !socket.connected) {
                resolve({ code: 500, msg: 'WebSocket未连接' });
                return;
              }
              
              socket.emit('get_model_info', { uid: '${uid}' }, (response) => {
                resolve(response || { code: 404, msg: '未收到响应' });
              });
              
              setTimeout(() => {
                resolve({ code: 408, msg: '请求超时' });
              }, 10000);
            });
          })()
        `);

        event.sender.send("model_info_response", result);
      } catch (error: any) {
        event.sender.send("model_info_response", {
          code: 500,
          msg: "执行请求失败: " + (error.message || "未知错误"),
        });
      }
    });
  }

  /**
   * 设置窗口相关的 IPC 处理器
   */
  private setupWindowHandlers() {
    // 新窗口处理
    ipcMain.handle("open-win", (_, arg) => {
      return this.windowManager.createChildWindow(arg);
    });

    // 主窗口准备显示
    ipcMain.on("APP_READY_TO_SHOW_MAIN_WINDOW", (event, args: any = {}) => {
      this.windowManager.createMainWindow(args.targetRoute);

      if (args.openedFilePath) {
        const sendFileData = () => {
          const win = this.windowManager.getMainWindow();
          if (args.targetRoute?.includes("/dataManagement/imandex")) {
            win?.webContents.send("excel-file-selected", args.openedFilePath);
          } else {
            win?.webContents.send(
              "workspace-file-selected",
              args.openedFilePath,
            );
          }

          if (args.singleFileMode) {
            win?.webContents.send("set-single-file-mode", args.openedFilePath);
          }
        };

        const win = this.windowManager.getMainWindow();
        win?.webContents.once("did-finish-load", sendFileData);
        win?.webContents.once("dom-ready", sendFileData);
        setTimeout(sendFileData, 1000);
      }
    });

    // 窗口控制
    ipcMain.on("minimize-window", () => {
      this.windowManager.minimizeWindow();
    });

    ipcMain.on("maximize-window", () => {
      this.windowManager.toggleMaximizeWindow();
    });

    ipcMain.on("close-window", () => {
      this.windowManager.closeWindow();
    });

    // 当前窗口控制
    ipcMain.on("minimize-current-window", (event) => {
      const currentWindow = BrowserWindow.fromWebContents(event.sender);
      if (currentWindow) {
        this.windowManager.minimizeWindow(currentWindow);
      }
    });

    ipcMain.on("maximize-current-window", (event) => {
      const currentWindow = BrowserWindow.fromWebContents(event.sender);
      if (currentWindow) {
        this.windowManager.toggleMaximizeWindow(currentWindow);
      }
    });

    ipcMain.on("close-current-window", (event) => {
      const currentWindow = BrowserWindow.fromWebContents(event.sender);
      if (currentWindow) {
        this.windowManager.closeWindow(currentWindow);
      }
    });

    // 文件选择事件转发
    ipcMain.on("workspace-file-selected", (event, filePath: string) => {
      const win = this.windowManager.getMainWindow();
      if (win && !win.isDestroyed()) {
        win.webContents.send("workspace-file-selected", filePath);
      }
    });

    ipcMain.on("excel-file-selected", (event, filePath: string) => {
      const win = this.windowManager.getMainWindow();
      if (win && !win.isDestroyed()) {
        win.webContents.send("excel-file-selected", filePath);
      }
    });

    // 应用退出
    ipcMain.on("app-quit", () => {
      app.quit();
    });
  }

  /**
   * 设置文件系统相关的 IPC 处理器
   */
  private setupFileSystemHandlers() {
    // 读取目录内容
    ipcMain.handle("fs:readDirectory", async (_, dirPath: string) => {
      return await this.fileSystemHandler.readDirectory(dirPath);
    });

    // 创建新目录
    ipcMain.handle("fs:createDirectory", async (_, targetPath: string) => {
      return await this.fileSystemHandler.createDirectory(targetPath);
    });

    // 创建新文件
    ipcMain.handle("fs:createFile", async (_, filePath: string) => {
      return await this.fileSystemHandler.createFile(filePath);
    });

    // 删除文件/目录
    ipcMain.handle("fs:deletePath", async (_, targetPath: string) => {
      return await this.fileSystemHandler.deletePath(targetPath);
    });

    // 重命名文件/目录
    ipcMain.handle("fs:rename", async (_, oldPath: string, newPath: string) => {
      return await this.fileSystemHandler.rename(oldPath, newPath);
    });

    // 检查文件/目录是否存在
    ipcMain.handle("fs:access", async (_, filePath: string) => {
      return await this.fileSystemHandler.access(filePath);
    });

    // 读取文件内容
    ipcMain.handle("fs:readFile", async (_, filePath: string) => {
      return await this.fileSystemHandler.readFile(filePath);
    });

    // 检测文件类型和读取内容
    ipcMain.handle("fs:readFileWithType", async (_, filePath: string) => {
      return await this.fileSystemHandler.readFileWithType(filePath);
    });

    // 读取文件内容为Buffer
    ipcMain.handle("fs:readFileBuffer", async (_, filePath: string) => {
      return await this.fileSystemHandler.readFileBuffer(filePath);
    });
    // 写入文件内容
    ipcMain.handle(
      "fs:writeFile",
      async (_, filePath: string, content: string) => {
        return await this.fileSystemHandler.writeFile(filePath, content);
      },
    );

    // 写入文件内容 (Buffer)
    ipcMain.handle(
      "fs:writeFileBuffer",
      async (_, filePath: string, arrayBuffer: ArrayBuffer) => {
        return await this.fileSystemHandler.writeFileBuffer(
          filePath,
          arrayBuffer,
        );
      },
    );

    // 写入文件内容 (Base64Buffer)
    ipcMain.handle(
      "fs:writeFileBase64Buffer",
      async (_, filePath: string, arrayBuffer: string) => {
        return await this.fileSystemHandler.writeFileBase64Buffer(
          filePath,
          arrayBuffer,
        );
      },
    );

    // 验证文件路径是否存在
    ipcMain.handle("fs:validatePath", async (_, filePath: string) => {
      return await this.fileSystemHandler.validatePath(filePath);
    });

    // 最近文件管理
    ipcMain.handle("recentFiles:get", async () => {
      return await this.fileSystemHandler.getRecentFiles();
    });

    ipcMain.handle("recentFiles:save", async (_, files: any[]) => {
      return await this.fileSystemHandler.saveRecentFiles(files);
    });

    // 获取目录大小
    ipcMain.handle("fs:getDirectorySize", async (_, dirPath: string) => {
      return await this.fileSystemHandler.getDirectorySize(dirPath);
    });

    // 递归读取目录
    ipcMain.handle("fs:readDirectoryRecursive", async (_, dirPath: string) => {
      return await this.fileSystemHandler.readDirectoryRecursive(dirPath);
    });

    // 检查路径是否存在
    ipcMain.handle("fs:pathExists", async (_, filePath: string) => {
      return await this.fileSystemHandler.pathExists(filePath);
    });
  }

  /**
   * 设置对话框相关的 IPC 处理器
   */
  private setupDialogHandlers() {
    // 选择目录对话框
    ipcMain.handle("dialog:openDirectory", async () => {
      return await this.dialogHandler.openDirectory();
    });

    // 选择文件对话框
    ipcMain.handle("dialog:openFile", async () => {
      return await this.dialogHandler.openFile();
    });

    // 选择保存文件对话框
    ipcMain.handle("dialog:saveFile", async (_, options: any) => {
      return await this.dialogHandler.saveFile(options);
    });

    // 项目管理相关
    ipcMain.handle("project:export", async (_, projectData: any) => {
      return await this.dialogHandler.exportProject(projectData);
    });

    ipcMain.handle("project:import", async () => {
      return await this.dialogHandler.importProject();
    });

    // 选择新的文件路径
    ipcMain.handle("dialog:selectNewPath", async (_, options: any) => {
      return await this.dialogHandler.selectNewPath(options);
    });

    // 显示对话框 (带窗口上下文)
    ipcMain.handle("dialog:showOpenDialog", async (event, options: any) => {
      const window = BrowserWindow.fromWebContents(event.sender);
      if (window) {
        return await this.dialogHandler.showOpenDialog(window, options);
      }
      return { canceled: true, filePaths: [] };
    });

    ipcMain.handle("dialog:showSaveDialog", async (event, options: any) => {
      const window = BrowserWindow.fromWebContents(event.sender);
      if (window) {
        return await this.dialogHandler.showSaveDialog(window, options);
      }
      return { canceled: true, filePath: "" };
    });
  }

  /**
   * 设置菜单相关的 IPC 处理器
   */
  private setupMenuHandlers() {
    // 显示各种菜单
    ipcMain.on("show-file-menu", (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createFileMenu(win);
      }
    });

    ipcMain.on("show-project-menu", (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createProjectMenu(win);
      }
    });

    ipcMain.on("show-edit-menu", (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createEditMenu(win);
      }
    });

    ipcMain.on("show-view-menu", (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createViewMenu(win);
      }
    });

    ipcMain.on("show-dev-menu", (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createDevMenu(win);
      }
    });

    ipcMain.on("show-about-menu", (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createAboutMenu(win);
      }
    });
  }

  /**
   * 设置实用工具相关的 IPC 处理器
   */
  private setupUtilityHandlers() {
    // 获取系统临时目录
    ipcMain.handle("os:tmpdir", async () => {
      return tmpdir();
    });

    // 路径拼接
    ipcMain.handle("path:join", async (_, ...paths: string[]) => {
      return join(...paths);
    });

    // 获取路径的目录部分
    ipcMain.handle("path:dirname", async (_, filePath: string) => {
      return dirname(filePath);
    });
  }
}

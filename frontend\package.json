{"name": "frontend", "version": "0.1.0", "description": "frontend", "private": true, "main": "dist-electron/main/index.js", "scripts": {"dev": "vite", "serve": "vite", "icon": "esno build/icon.ts --input=public/icon.png --output=dist", "build": "rimraf dist && rimraf release && cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build && yarn icon && electron-builder", "build:staging": "rimraf dist && rimraf release && vite build --mode staging && yarn icon && electron-builder", "browser:dev": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite", "browser:build": "rimraf dist && cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build", "preview": "vite preview", "preview:build": "yarn browser:build && vite preview", "report": "rimraf dist && vite build", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf node_modules && yarn cache clean && yarn install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "yarn lint:eslint && yarn lint:prettier && yarn lint:stylelint", "prepare": "husky"}, "keywords": ["electron-pure-admin", "vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "electron", "pinia", "vue3", "vite", "esm"], "homepage": "", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/electron-pure-admin.git"}, "bugs": {}, "license": "MIT", "author": {}, "dependencies": {"@handsontable/vue3": "^15.2.0", "@iconify/json": "^2.2.375", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.2.1", "@pureadmin/utils": "^2.5.0", "@types/crypto-js": "^4.2.2", "@vueuse/core": "^12.0.0", "@vueuse/motion": "^2.2.6", "animate.css": "^4.1.1", "axios": "^1.7.9", "core-js": "^3.44.0", "crypto-js": "^4.2.0", "d3": "^7.9.0", "d3-graphviz": "^5.6.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "element-plus": "^2.9.0", "handsontable": "^15.2.0", "js-cookie": "^3.0.5", "localforage": "^1.10.0", "mathjax-full": "^3.2.2", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pdfjs-dist": "^5.4.54", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "pinyin-pro": "^3.26.0", "qs": "^6.13.1", "responsive-storage": "^2.2.0", "simple-statistics": "^7.8.8", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.6", "tippy.js": "^6.3.7", "vue": "^3.5.13", "vue-demi": "^0.14.10", "vue-router": "^4.5.0", "vue-tippy": "^6.5.0", "vue-types": "^5.1.3", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@commitlint/types": "^19.5.0", "@element-plus/icons-vue": "^2.3.1", "@eslint/js": "^9.16.0", "@faker-js/faker": "^9.3.0", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.2.0", "@types/args": "^5.0.3", "@types/d3": "^7.4.3", "@types/js-cookie": "^3.0.6", "@types/node": "^20.17.9", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.17", "@types/socket.io-client": "^3.0.0", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "adm-zip": "^0.5.16", "args": "^5.0.3", "autoprefixer": "^10.4.20", "boxen": "^8.0.1", "code-inspector-plugin": "^0.18.2", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "electron": "^22.2.0", "electron-builder": "^25.1.8", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "esno": "^4.7.0", "gradient-string": "^3.0.0", "husky": "^9.1.7", "icon-gen": "^5.0.0", "jimp": "0.22.12", "jszip": "^3.10.1", "lint-staged": "^15.5.1", "lodash-es": "^4.17.21", "mammoth": "^1.9.1", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.4.2", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.82.0", "stylelint": "^16.11.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.2", "svgo": "^3.3.2", "tailwindcss": "^3.4.16", "typescript": "5.6.3", "unplugin-icons": "^22.1.0", "vite": "^6.0.3", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-fake-server": "^2.1.4", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.1.10"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=22.0.0"}}